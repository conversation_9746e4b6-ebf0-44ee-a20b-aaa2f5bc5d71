const express = require('express')
const multer = require('multer')
const path = require('path')

const router = express.Router()

const remoteUpload_handler = require('../router_handler/remoteUpload')

// 文件过滤器 - 只允许文本文档
const fileFilter = (req, file, cb) => {
  // 允许文本文件
  if (file.mimetype === 'text/plain' || path.extname(file.originalname).toLowerCase() === '.txt') {
    cb(null, true)
  } else {
    cb(new Error('只允许上传文本文档'), false)
  }
}

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../upload/temp/'))
  },
  filename: function (req, file, cb) {
    cb(null, file.originalname)
  }
})

const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024 // 限制文件大小为2MB
  }
})

router.post('/upload', upload.single('file'), remoteUpload_handler.uploadFile)

module.exports = router
