const express = require('express')
const path = require('path')
const app = express()
const Joi = require('joi')

const cors = require('cors')
app.use(cors())

app.use(express.urlencoded({ extended: false }))
app.use(express.json())

// 提供静态文件服务，用于访问上传的游戏文件
app.use('/upload', express.static(path.join(__dirname, 'upload')))

app.use(function(req,res,next){
  res.cc = function(err, status = 1) {
    res.send({
      status,
      message: err instanceof Error ? err.message : err
    })
  }
  next()
})

const config = require('./config')
const expressJWT = require('express-jwt')

app.use(expressJWT({
  secret: config.jwtSecretKey,
  algorithms: ['HS256'] }).unless({ path: [/^\/login/, /^\/gameDesc/, /^\/game/, /^\/GamePlay/, /^\/register/, /^\/upload\/game/] } ))
  
// 错误处理中间件必须放在最后
app.use(function(err, req, res, next){
  if(err instanceof Joi.ValidationError) return res.cc(err)
  if(err.name === 'UnauthorizedError') return res.cc('身份认证失败')
  res.cc(err)
})

const userRouter = require('./router/user.js')
const gameRouter = require('./router/games.js')
const playGameRouter = require('./router/playGame.js')
const remoteUpload = require('./router/remoteUpload.js')
app.use(userRouter)
app.use(gameRouter)
app.use('/upload',remoteUpload)
app.use('/GamePlay', playGameRouter)

app.listen(3000, () => {
  console.log('Server started on 47.86.12.151:3000')
})
