
<!DOCTYPE html>

<html>

<head>

    <meta charset="UTF-8">

    <title>深圳龙华区积分入学信息收集表单</title>

    <style>

        label { display: block; margin-top: 12px; }

        select, input[type="text"], input[type="date"] { width: 220px; }

        .other-input { margin-left: 8px; }

    </style>

    <script>

        function toggleOther(selectObj, inputId) {

            var value = selectObj.value;

            document.getElementById(inputId).style.display = value === '其他' ? 'inline-block' : 'none';

        }


        function getValue(selectId, otherId) {

            var select = document.getElementsByName(selectId)[0];

            var value = select.value;

            if (value === '其他') {

                var other = document.getElementById(otherId).value;

                return other ? other : '未填写';

            }

            return value;

        }


        function downloadMarkdown(e) {

            e.preventDefault(); // 阻止表单提交

            // 获取所有值

            let result = [];

            result.push(`# 深圳龙华区积分入学信息综合报告\n`);

            result.push(`**1. 孩子姓名：** ${document.getElementsByName('child_name')[0].value}`);

            result.push(`**2. 性别：** ${document.getElementsByName('child_gender')[0].value}`);

            result.push(`**3. 出生日期：** ${document.getElementsByName('child_birthday')[0].value}`);

            result.push(`**4. 申请年级：** ${document.getElementsByName('grade')[0].value}`);

            result.push(`**5. 孩子户籍类型：** ${getValue('child_hukou', 'hukou_other')}`);

            result.push(`**6. 父母/监护人户籍情况：** ${getValue('parent_hukou', 'parent_hukou_other')}`);

            result.push(`**7. 学区内住房类型：** ${getValue('housing_type', 'housing_other')}`);

            result.push(`**8. 住房所有权归属：** ${getValue('housing_owner', 'housing_owner_other')}`);

            result.push(`**9. 是否实际居住于上述住房/已办理居住信息登记：** ${document.getElementsByName('actual_living')[0].value}`);

            result.push(`**10. 父母/监护人深圳社保缴纳情况（养老+医疗）：** ${getValue('social_insurance', 'social_insurance_other')}`);

            result.push(`**11. 孩子当前学籍情况：** ${document.getElementsByName('school_status')[0].value}`);

            result.push(`**12. 家庭是否有特殊情况需说明：** ${document.getElementsByName('special_case')[0].value}`);

            result.push(`**13. 联系方式：** ${document.getElementsByName('contact')[0].value}`);

            // 合成Markdown内容

            let mdContent = result.join('\n\n');

            // 下载

            let blob = new Blob([mdContent], {type: "text/markdown"});

            let url = URL.createObjectURL(blob);

            let a = document.createElement('a');

            a.href = url;

            a.download = "积分入学信息报告.md";

            document.body.appendChild(a);

            a.click();

            document.body.removeChild(a);

            URL.revokeObjectURL(url);

        }

    </script>

</head>

<body>

    <h2>深圳龙华区积分入学信息收集表单</h2>

    <form id="scoreForm">

        <label>

            1. 孩子姓名：

            <input type="text" name="child_name" required>

        </label>

        <label>

            2. 性别：

            <select name="child_gender">

                <option value="男">男</option>

                <option value="女">女</option>

                <option value="其他">其他</option>

            </select>

        </label>

        <label>

            3. 出生日期：

            <input type="date" name="child_birthday" required>

        </label>

        <label>

            4. 申请年级：

            <select name="grade">

                <option value="小学一年级">小学一年级</option>

                <option value="初中一年级">初中一年级</option>

                <option value="其他">其他</option>

            </select>

        </label>

        <label>

            5. 孩子户籍类型：

            <select name="child_hukou" onchange="toggleOther(this, 'hukou_other')">

                <option value="龙华区户籍">龙华区户籍</option>

                <option value="深圳其他区户籍">深圳其他区户籍</option>

                <option value="非深户籍">非深户籍</option>

                <option value="港澳台">港澳台</option>

                <option value="外籍">外籍</option>

                <option value="其他">其他</option>

            </select>

            <input type="text" id="hukou_other" name="hukou_other" class="other-input" placeholder="请注明" style="display:none;">

        </label>

        <label>

            6. 父母/监护人户籍情况：

            <select name="parent_hukou" onchange="toggleOther(this, 'parent_hukou_other')">

                <option value="父母双方均为龙华户籍">父母双方均为龙华户籍</option>

                <option value="父母一方为龙华户籍">父母一方为龙华户籍</option>

                <option value="父母双方均为深圳其他区户籍">父母双方均为深圳其他区户籍</option>

                <option value="父母一方为深圳其他区户籍">父母一方为深圳其他区户籍</option>

                <option value="父母持有深圳居住证">父母持有深圳居住证</option>

                <option value="其他">其他</option>

            </select>

            <input type="text" id="parent_hukou_other" name="parent_hukou_other" class="other-input" placeholder="请注明" style="display:none;">

        </label>

        <label>

            7. 学区内住房类型：

            <select name="housing_type" onchange="toggleOther(this, 'housing_other')">

                <option value="自购商品房（产权≥51%）">自购商品房（产权≥51%）</option>

                <option value="原著居民住房">原著居民住房</option>

                <option value="安居型住房">安居型住房</option>

                <option value="租房（有房屋租赁凭证）">租房（有房屋租赁凭证）</option>

                <option value="集体宿舍">集体宿舍</option>

                <option value="其他类住房">其他类住房</option>

                <option value="无住房">无住房</option>

                <option value="其他">其他</option>

            </select>

            <input type="text" id="housing_other" name="housing_other" class="other-input" placeholder="请注明" style="display:none;">

        </label>

        <label>

            8. 住房所有权归属：

            <select name="housing_owner" onchange="toggleOther(this, 'housing_owner_other')">

                <option value="父母">父母</option>

                <option value="祖父母/外祖父母">祖父母/外祖父母</option>

                <option value="法定监护人">法定监护人</option>

                <option value="其他">其他</option>

            </select>

            <input type="text" id="housing_owner_other" name="housing_owner_other" class="other-input" placeholder="请注明" style="display:none;">

        </label>

        <label>

            9. 是否实际居住于上述住房/已办理居住信息登记？

            <select name="actual_living">

                <option value="是">是</option>

                <option value="否">否</option>

            </select>

        </label>

        <label>

            10. 父母/监护人深圳社保缴纳情况（养老+医疗）：

            <select name="social_insurance" onchange="toggleOther(this, 'social_insurance_other')">

                <option value="满一年以上且在缴">满一年以上且在缴</option>

                <option value="不满一年">不满一年</option>

                <option value="已退休">已退休</option>

                <option value="无法提供深圳社保">无法提供深圳社保</option>

                <option value="其他">其他</option>

            </select>

            <input type="text" id="social_insurance_other" name="social_insurance_other" class="other-input" placeholder="请注明" style="display:none;">

        </label>

        <label>

            11. 孩子当前学籍情况：

            <select name="school_status">

                <option value="无学籍/未入学">无学籍/未入学</option>

                <option value="已取得小学学籍">已取得小学学籍</option>

                <option value="已取得初中学籍">已取得初中学籍</option>

                <option value="其他">其他</option>

            </select>

        </label>

        <label>

            12. 家庭是否有特殊情况需说明（选填）：

            <input type="text" name="special_case" placeholder="如军人子女、高层次人才、残疾等">

        </label>

        <label>

            13. 联系方式（便于后续沟通）：

            <input type="text" name="contact" placeholder="手机或微信号">

        </label>

        <br>

        <button onclick="downloadMarkdown(event)">生成Markdown报告并下载</button>

    </form>

</body>

</html>

