<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Remote Game</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background-color: #f0f0f0;
        }
        .game-container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 32px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        #score {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>Test Remote Game</h1>
        <p>This is a simple test game uploaded via remote upload.</p>
        <div id="score">Score: 0</div>
        <button onclick="increaseScore()">Click to Score!</button>
        <button onclick="resetScore()">Reset</button>
    </div>

    <script>
        let score = 0;
        
        function increaseScore() {
            score += 10;
            document.getElementById('score').textContent = 'Score: ' + score;
        }
        
        function resetScore() {
            score = 0;
            document.getElementById('score').textContent = 'Score: ' + score;
        }
    </script>
</body>
</html>
