# 远程上传文件格式说明

## 概述
远程上传功能现在支持通过文本文档上传游戏，文档内容包含游戏名称和HTML代码。

## 文件格式要求

### 文件类型
- 必须是文本文档（.txt 文件）
- MIME类型：text/plain

### 文件内容格式
```
游戏名称
HTML内容（从第二行开始）
```

### 示例
```
my_awesome_game
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>My Awesome Game</title>
</head>
<body>
    <h1>Welcome to My Game!</h1>
    <p>This is a simple game.</p>
</body>
</html>
```

## 游戏名称规则
- 只能包含字母、数字和下划线
- 不能为空
- 不能与现有游戏重名
- 正则表达式：`^[a-zA-Z0-9_]+$`

## 上传接口
- **URL**: `POST /upload/upload`
- **参数**: `file` (multipart/form-data)
- **认证**: 不需要身份验证

## 响应格式

### 成功响应
```json
{
  "status": 0,
  "message": "游戏上传成功",
  "data": {
    "game_id": 9,
    "game_name": "my_awesome_game",
    "game_desc": "",
    "file_path": "upload/my_awesome_game/my_awesome_game.html"
  }
}
```

### 错误响应
```json
{
  "status": 1,
  "message": "错误信息"
}
```

## 常见错误
- `请选择要上传的文件` - 没有上传文件
- `只允许上传文本文档` - 文件类型不正确
- `文件格式错误，应包含游戏名称和HTML内容` - 文件内容格式不正确
- `游戏名称不能为空` - 第一行为空
- `游戏名称只能包含字母、数字和下划线` - 游戏名称格式不符合要求
- `HTML内容不能为空` - HTML内容为空
- `游戏名已存在` - 游戏名称重复

## 文件存储
- HTML文件保存路径：`upload/{游戏名称}/{游戏名称}.html`
- 数据库记录：games表，game_desc字段为空字符串

## 使用示例

### 使用curl测试
```bash
curl -X POST -F "file=@game.txt" http://localhost:3000/upload/upload
```

### 文件内容示例 (game.txt)
```
simple_clicker
<!DOCTYPE html>
<html>
<head>
    <title>Simple Clicker</title>
    <style>
        body { text-align: center; padding: 50px; }
        button { padding: 20px; font-size: 18px; }
    </style>
</head>
<body>
    <h1>Simple Clicker Game</h1>
    <p>Score: <span id="score">0</span></p>
    <button onclick="click()">Click Me!</button>
    <script>
        let score = 0;
        function click() {
            score++;
            document.getElementById('score').textContent = score;
        }
    </script>
</body>
</html>
```
