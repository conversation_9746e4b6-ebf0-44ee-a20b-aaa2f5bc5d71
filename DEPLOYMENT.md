# 阿里云生产环境部署指南

## 前置条件

1. **阿里云 ECS 服务器**
   - Ubuntu 20.04 或更高版本
   - 至少 2GB RAM
   - 至少 20GB 存储空间

2. **域名配置**
   - 域名：`games.hkbu.life`
   - DNS A 记录指向服务器 IP：`************`

3. **SSL 证书**
   - Let's Encrypt 证书已申请
   - 证书文件已上传到服务器

## 部署步骤

### 1. 上传项目文件
```bash
# 将整个项目上传到服务器的 /var/www/games 目录
scp -r ./games root@************:/var/www/
```

### 2. 配置 SSL 证书
```bash
# 在生产环境中，将证书文件放到正确位置
mkdir -p /var/www/games/buGames_backend/ssl
cp /etc/letsencrypt/live/games.hkbu.life/privkey.pem /var/www/games/buGames_backend/ssl/
cp /etc/letsencrypt/live/games.hkbu.life/fullchain.pem /var/www/games/buGames_backend/ssl/
```

### 3. 运行部署脚本
```bash
cd /var/www/games
chmod +x deploy.sh
./deploy.sh
```

### 4. 配置数据库
```bash
# 登录 MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE bugamedb;
CREATE USER 'gameuser'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON bugamedb.* TO 'gameuser'@'localhost';
FLUSH PRIVILEGES;

# 导入数据库结构和数据
mysql -u gameuser -p bugamedb < database.sql
```

### 5. 更新数据库配置
编辑 `buGames_backend/db/index.js`：
```javascript
const db = mysql.createPool({
  host: '127.0.0.1',
  user: 'gameuser',
  password: 'your_password',
  database: 'bugamedb'
})
```

## 验证部署

### 1. 检查服务状态
```bash
# 检查 PM2 进程
pm2 status

# 检查 Nginx 状态
systemctl status nginx

# 检查端口监听
netstat -tlnp | grep :443
netstat -tlnp | grep :3000
```

### 2. 测试访问
- 前端：https://games.hkbu.life
- API：https://games.hkbu.life:3000/game

### 3. 查看日志
```bash
# PM2 日志
pm2 logs games-backend

# Nginx 日志
tail -f /var/log/nginx/games-access.log
tail -f /var/log/nginx/games-error.log
```

## 维护命令

### 重启服务
```bash
# 重启后端
pm2 restart games-backend

# 重启 Nginx
systemctl restart nginx
```

### 更新应用
```bash
# 更新前端
cd /var/www/games/buGames
git pull
npm run build

# 更新后端
cd /var/www/games/buGames_backend
git pull
npm install --production
pm2 restart games-backend
```

### SSL 证书续期
```bash
# Let's Encrypt 自动续期
certbot renew --dry-run

# 手动续期
certbot renew
systemctl reload nginx
```

## 故障排除

### 1. 证书问题
- 检查证书文件路径和权限
- 验证域名 DNS 解析
- 检查防火墙设置

### 2. 数据库连接问题
- 检查 MySQL 服务状态
- 验证数据库用户权限
- 检查连接配置

### 3. 前端无法访问后端
- 检查 Nginx 代理配置
- 验证后端服务是否运行
- 检查 CORS 设置

## 安全建议

1. **定期更新系统和软件包**
2. **配置防火墙规则**
3. **定期备份数据库**
4. **监控服务器资源使用情况**
5. **设置日志轮转**

## 监控和备份

### 设置监控
```bash
# 安装监控工具
npm install -g pm2-logrotate
pm2 install pm2-server-monit
```

### 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u gameuser -p bugamedb > /backup/bugamedb_$DATE.sql
```
