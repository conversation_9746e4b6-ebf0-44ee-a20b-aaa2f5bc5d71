// HTTP请求工具
const BASE_URL = 'http://47.86.12.151:3000'
// const BASE_URL = 'https://games.hkbu.life/api'
// const BASE_URL = 'http://127.0.0.1:3000/api'

// 通用请求函数
export const request = async (url, options = {}) => {
  const token = localStorage.getItem('token')
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': token })
    }
  }
  
  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  }
  
  try {
    const response = await fetch(`${BASE_URL}${url}`, finalOptions)
    const data = await response.json()
    return data
  } catch (error) {
    console.error('请求失败:', error)
    throw error
  }
}

// 登录API
export const loginAPI = (username, password) => {
  return request('/login', {
    method: 'POST',
    body: JSON.stringify({ username, password })
  })
}

// 添加新游戏API (使用FormData上传文件)
export const addNewGameAPI = (formData) => {
  const token = localStorage.getItem('token')

  return fetch(`${BASE_URL}/newGame`, {
    method: 'POST',
    headers: {
      ...(token && { 'Authorization': token })
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
    },
    body: formData
  }).then(response => response.json())
}

// 编辑游戏API (使用FormData上传文件)
export const editGameAPI = (formData) => {
  const token = localStorage.getItem('token')
  
  return fetch(`${BASE_URL}/editGame`, {
    method: 'POST',
    headers: {
      ...(token && { 'Authorization': token })
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
    },
    body: formData
  }).then(response => response.json())
}

// 获取游戏列表API
export const getGameListAPI = () => {
  return request('/game', {
    method: 'GET'
  })
}

// 获取游戏详情API
export const getGameDescAPI = (gameId) => {
  return request(`/gameDesc/${gameId}`, {
    method: 'GET'
  })
}

// 删除游戏API
export const deleteGameAPI = (gameId) => {
  return request(`/deleteGame/${gameId}`, {
    method: 'DELETE'
  })
}
