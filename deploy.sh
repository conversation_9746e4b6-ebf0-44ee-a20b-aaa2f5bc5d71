#!/bin/bash

# 游戏应用部署脚本
echo "开始部署游戏应用..."

# 设置变量
PROJECT_DIR="/var/www/games"
BACKEND_DIR="$PROJECT_DIR/buGames_backend"
FRONTEND_DIR="$PROJECT_DIR/buGames"
NGINX_CONF="/etc/nginx/sites-available/games.hkbu.life"
PM2_APP_NAME="games-backend"

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 root 权限运行此脚本"
    exit 1
fi

# 1. 更新系统包
echo "更新系统包..."
apt update && apt upgrade -y

# 2. 安装必要的软件
echo "安装必要的软件..."
apt install -y nginx nodejs npm mysql-server

# 3. 安装 PM2 (用于管理 Node.js 进程)
echo "安装 PM2..."
npm install -g pm2

# 4. 创建项目目录
echo "创建项目目录..."
mkdir -p $PROJECT_DIR
cd $PROJECT_DIR

# 5. 复制项目文件 (假设已经上传到服务器)
echo "请确保项目文件已经上传到 $PROJECT_DIR"

# 6. 安装后端依赖
echo "安装后端依赖..."
cd $BACKEND_DIR
npm install --production

# 7. 安装前端依赖并构建
echo "构建前端..."
cd $FRONTEND_DIR
npm install
npm run build

# 8. 配置 SSL 证书路径 (需要手动调整)
echo "配置 SSL 证书..."
SSL_CERT_DIR="$BACKEND_DIR/ssl/47.86.12.151_1751616596/Nginx"
if [ -d "$SSL_CERT_DIR" ]; then
    echo "SSL 证书目录存在: $SSL_CERT_DIR"
else
    echo "警告: SSL 证书目录不存在，请检查路径"
fi

# 9. 配置 Nginx
echo "配置 Nginx..."
cp nginx-production.conf $NGINX_CONF

# 更新 nginx 配置中的路径
sed -i "s|/path/to/your/vue/dist|$FRONTEND_DIR/dist|g" $NGINX_CONF
sed -i "s|/path/to/your/ssl/fullchain.pem|$SSL_CERT_DIR/fullchain.pem|g" $NGINX_CONF
sed -i "s|/path/to/your/ssl/privkey.pem|$SSL_CERT_DIR/privkey.key|g" $NGINX_CONF

# 启用站点
ln -sf $NGINX_CONF /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# 测试 nginx 配置
nginx -t
if [ $? -eq 0 ]; then
    echo "Nginx 配置测试通过"
    systemctl reload nginx
else
    echo "Nginx 配置测试失败，请检查配置"
    exit 1
fi

# 10. 启动后端服务
echo "启动后端服务..."
cd $BACKEND_DIR
pm2 start app.js --name $PM2_APP_NAME
pm2 save
pm2 startup

# 11. 设置防火墙
echo "配置防火墙..."
ufw allow 22
ufw allow 80
ufw allow 443
ufw allow 3000
ufw --force enable

# 12. 启动服务
echo "启动服务..."
systemctl enable nginx
systemctl start nginx
systemctl enable mysql

echo "部署完成！"
echo "前端访问地址: https://games.hkbu.life"
echo "后端 API 地址: https://games.hkbu.life:3000"
echo ""
echo "请检查以下项目："
echo "1. DNS 记录是否指向服务器 IP"
echo "2. SSL 证书是否正确配置"
echo "3. 数据库是否正确配置"
echo "4. PM2 进程是否正常运行: pm2 status"
echo "5. Nginx 是否正常运行: systemctl status nginx"
