module.exports = {
  apps: [{
    name: 'games-backend',
    script: './app.js',
    cwd: './buGames_backend',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }],

  deploy: {
    production: {
      user: 'root',
      host: '************',
      ref: 'origin/main',
      repo: 'your-git-repo-url',
      path: '/var/www/games',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
